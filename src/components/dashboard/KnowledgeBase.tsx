"use client";

import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Checkbox } from "@/components/ui/checkbox";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Upload,
  FileText,
  Database,
  Search,
  Plus,
  MoreHorizontal,
  Edit,
  Trash2,
  Download,
  Eye,
  Link,
  CheckCircle,
  Clock,
  AlertTriangle,
  XCircle,
  Bot,
  GitBranch,
  Filter,
  RefreshCw,
  FileIcon,
  Image,
  Video,
  Music,
  Archive,
} from "lucide-react";

interface KnowledgeBaseProps {
  className?: string;
}

interface Document {
  id: string;
  name: string;
  type: string;
  size: string;
  uploadDate: string;
  status: "processing" | "ready" | "error" | "indexing";
  chunks: number;
  linkedAgents: string[];
  linkedWorkflows: string[];
  category: string;
}

interface KnowledgeBaseStats {
  totalDocuments: number;
  totalSize: string;
  processedChunks: number;
  linkedAgents: number;
  linkedWorkflows: number;
}

export default function KnowledgeBase({ className = "" }: KnowledgeBaseProps) {
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false);
  const [linkDialogOpen, setLinkDialogOpen] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(
    null,
  );
  const [searchQuery, setSearchQuery] = useState("");
  const [filterCategory, setFilterCategory] = useState("all");
  const [filterStatus, setFilterStatus] = useState("all");
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);

  const stats: KnowledgeBaseStats = {
    totalDocuments: 24,
    totalSize: "156.7 MB",
    processedChunks: 1847,
    linkedAgents: 8,
    linkedWorkflows: 5,
  };

  const documents: Document[] = [
    {
      id: "1",
      name: "Product Documentation.pdf",
      type: "pdf",
      size: "2.4 MB",
      uploadDate: "2024-01-15",
      status: "ready",
      chunks: 156,
      linkedAgents: ["Customer Support Agent", "Product Expert"],
      linkedWorkflows: ["Customer Onboarding"],
      category: "documentation",
    },
    {
      id: "2",
      name: "API Reference Guide.md",
      type: "markdown",
      size: "890 KB",
      uploadDate: "2024-01-14",
      status: "ready",
      chunks: 89,
      linkedAgents: ["Developer Assistant"],
      linkedWorkflows: ["API Support Workflow"],
      category: "technical",
    },
    {
      id: "3",
      name: "Company Policies.docx",
      type: "docx",
      size: "1.2 MB",
      uploadDate: "2024-01-13",
      status: "processing",
      chunks: 0,
      linkedAgents: [],
      linkedWorkflows: [],
      category: "policy",
    },
    {
      id: "4",
      name: "Training Materials.zip",
      type: "zip",
      size: "15.6 MB",
      uploadDate: "2024-01-12",
      status: "error",
      chunks: 0,
      linkedAgents: [],
      linkedWorkflows: [],
      category: "training",
    },
    {
      id: "5",
      name: "FAQ Database.json",
      type: "json",
      size: "456 KB",
      uploadDate: "2024-01-11",
      status: "indexing",
      chunks: 234,
      linkedAgents: ["FAQ Bot"],
      linkedWorkflows: [],
      category: "support",
    },
  ];

  const categories = [
    { value: "all", label: "All Categories" },
    { value: "documentation", label: "Documentation" },
    { value: "technical", label: "Technical" },
    { value: "policy", label: "Policy" },
    { value: "training", label: "Training" },
    { value: "support", label: "Support" },
  ];

  const statusOptions = [
    { value: "all", label: "All Status" },
    { value: "ready", label: "Ready" },
    { value: "processing", label: "Processing" },
    { value: "indexing", label: "Indexing" },
    { value: "error", label: "Error" },
  ];

  const availableAgents = [
    "Customer Support Agent",
    "Product Expert",
    "Developer Assistant",
    "FAQ Bot",
    "Sales Assistant",
  ];

  const availableWorkflows = [
    "Customer Onboarding",
    "API Support Workflow",
    "Lead Qualification",
    "Issue Resolution",
    "Product Demo",
  ];

  const handleFileUpload = async (files: FileList | null) => {
    if (!files || files.length === 0) return;

    setIsUploading(true);
    setUploadProgress(0);

    // Simulate file upload progress
    const interval = setInterval(() => {
      setUploadProgress((prev) => {
        if (prev >= 100) {
          clearInterval(interval);
          setIsUploading(false);
          setUploadDialogOpen(false);
          return 100;
        }
        return prev + 10;
      });
    }, 200);
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      ready: "default",
      processing: "secondary",
      indexing: "outline",
      error: "destructive",
    } as const;

    const icons = {
      ready: <CheckCircle className="mr-1 h-3 w-3" />,
      processing: <Clock className="mr-1 h-3 w-3" />,
      indexing: <RefreshCw className="mr-1 h-3 w-3 animate-spin" />,
      error: <XCircle className="mr-1 h-3 w-3" />,
    };

    return (
      <Badge variant={variants[status as keyof typeof variants]}>
        {icons[status as keyof typeof icons]}
        {status}
      </Badge>
    );
  };

  const getFileIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case "pdf":
        return <FileText className="h-4 w-4 text-red-500" />;
      case "docx":
      case "doc":
        return <FileText className="h-4 w-4 text-blue-500" />;
      case "markdown":
      case "md":
        return <FileText className="h-4 w-4 text-gray-500" />;
      case "json":
        return <Database className="h-4 w-4 text-green-500" />;
      case "zip":
      case "rar":
        return <Archive className="h-4 w-4 text-yellow-500" />;
      case "jpg":
      case "png":
      case "gif":
        return <Image className="h-4 w-4 text-purple-500" />;
      case "mp4":
      case "avi":
        return <Video className="h-4 w-4 text-red-500" />;
      case "mp3":
      case "wav":
        return <Music className="h-4 w-4 text-green-500" />;
      default:
        return <FileIcon className="h-4 w-4 text-gray-500" />;
    }
  };

  const filteredDocuments = documents.filter((doc) => {
    const matchesSearch = doc.name
      .toLowerCase()
      .includes(searchQuery.toLowerCase());
    const matchesCategory =
      filterCategory === "all" || doc.category === filterCategory;
    const matchesStatus = filterStatus === "all" || doc.status === filterStatus;
    return matchesSearch && matchesCategory && matchesStatus;
  });

  return (
    <div className={`bg-background min-h-screen p-6 ${className}`}>
      <div className="max-w-7xl mx-auto">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              Knowledge Base
            </h1>
            <p className="text-muted-foreground">
              Manage documents, process for RAG, and link to agents/workflows
            </p>
          </div>
          <Dialog open={uploadDialogOpen} onOpenChange={setUploadDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Upload className="mr-2 h-4 w-4" />
                Upload Documents
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Upload Documents</DialogTitle>
                <DialogDescription>
                  Upload documents to your knowledge base for RAG processing
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-8 text-center">
                  <Upload className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                  <p className="text-lg font-medium mb-2">
                    Drop files here or click to browse
                  </p>
                  <p className="text-sm text-muted-foreground mb-4">
                    Supports PDF, DOCX, MD, TXT, JSON and more
                  </p>
                  <input
                    type="file"
                    multiple
                    className="hidden"
                    id="file-upload"
                    onChange={(e) => handleFileUpload(e.target.files)}
                  />
                  <Button asChild>
                    <label htmlFor="file-upload" className="cursor-pointer">
                      Select Files
                    </label>
                  </Button>
                </div>

                {isUploading && (
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Uploading...</span>
                      <span>{uploadProgress}%</span>
                    </div>
                    <Progress value={uploadProgress} />
                  </div>
                )}

                <div className="space-y-2">
                  <Label htmlFor="category">Category</Label>
                  <Select defaultValue="documentation">
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.slice(1).map((category) => (
                        <SelectItem key={category.value} value={category.value}>
                          {category.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description (Optional)</Label>
                  <Textarea
                    id="description"
                    placeholder="Describe the content of these documents..."
                  />
                </div>
              </div>
              <DialogFooter>
                <Button
                  variant="outline"
                  onClick={() => setUploadDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button disabled={isUploading}>
                  {isUploading ? "Uploading..." : "Upload"}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>

        {/* Stats Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5 mb-6">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">
                Total Documents
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalDocuments}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Total Size</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalSize}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">
                Processed Chunks
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {stats.processedChunks.toLocaleString()}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">
                Linked Agents
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.linkedAgents}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">
                Linked Workflows
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.linkedWorkflows}</div>
            </CardContent>
          </Card>
        </div>

        <Tabs defaultValue="documents" className="space-y-6">
          <TabsList>
            <TabsTrigger value="documents">
              <FileText className="mr-2 h-4 w-4" />
              Documents
            </TabsTrigger>
            <TabsTrigger value="processing">
              <RefreshCw className="mr-2 h-4 w-4" />
              Processing Queue
            </TabsTrigger>
            <TabsTrigger value="links">
              <Link className="mr-2 h-4 w-4" />
              Agent/Workflow Links
            </TabsTrigger>
          </TabsList>

          <TabsContent value="documents" className="space-y-6">
            {/* Filters */}
            <Card>
              <CardContent className="pt-6">
                <div className="flex flex-col sm:flex-row gap-4">
                  <div className="flex-1">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder="Search documents..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="pl-10"
                      />
                    </div>
                  </div>
                  <Select
                    value={filterCategory}
                    onValueChange={setFilterCategory}
                  >
                    <SelectTrigger className="w-48">
                      <Filter className="mr-2 h-4 w-4" />
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map((category) => (
                        <SelectItem key={category.value} value={category.value}>
                          {category.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <Select value={filterStatus} onValueChange={setFilterStatus}>
                    <SelectTrigger className="w-40">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {statusOptions.map((status) => (
                        <SelectItem key={status.value} value={status.value}>
                          {status.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>

            {/* Documents Table */}
            <Card>
              <CardHeader>
                <CardTitle>Documents ({filteredDocuments.length})</CardTitle>
                <CardDescription>
                  Manage your knowledge base documents and their processing
                  status
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Document</TableHead>
                      <TableHead>Category</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Chunks</TableHead>
                      <TableHead>Linked To</TableHead>
                      <TableHead>Upload Date</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredDocuments.map((doc) => (
                      <TableRow key={doc.id}>
                        <TableCell>
                          <div className="flex items-center space-x-3">
                            {getFileIcon(doc.type)}
                            <div>
                              <p className="font-medium">{doc.name}</p>
                              <p className="text-sm text-muted-foreground">
                                {doc.size} • {doc.type.toUpperCase()}
                              </p>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">{doc.category}</Badge>
                        </TableCell>
                        <TableCell>{getStatusBadge(doc.status)}</TableCell>
                        <TableCell>
                          {doc.chunks > 0 ? doc.chunks.toLocaleString() : "-"}
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            {doc.linkedAgents.length > 0 && (
                              <div className="flex items-center space-x-1">
                                <Bot className="h-3 w-3" />
                                <span className="text-sm">
                                  {doc.linkedAgents.length} agents
                                </span>
                              </div>
                            )}
                            {doc.linkedWorkflows.length > 0 && (
                              <div className="flex items-center space-x-1">
                                <GitBranch className="h-3 w-3" />
                                <span className="text-sm">
                                  {doc.linkedWorkflows.length} workflows
                                </span>
                              </div>
                            )}
                            {doc.linkedAgents.length === 0 &&
                              doc.linkedWorkflows.length === 0 && (
                                <span className="text-sm text-muted-foreground">
                                  Not linked
                                </span>
                              )}
                          </div>
                        </TableCell>
                        <TableCell className="text-sm text-muted-foreground">
                          {doc.uploadDate}
                        </TableCell>
                        <TableCell>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem>
                                <Eye className="mr-2 h-4 w-4" />
                                Preview
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={() => {
                                  setSelectedDocument(doc);
                                  setLinkDialogOpen(true);
                                }}
                              >
                                <Link className="mr-2 h-4 w-4" />
                                Manage Links
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <Download className="mr-2 h-4 w-4" />
                                Download
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem>
                                <Edit className="mr-2 h-4 w-4" />
                                Edit Metadata
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <RefreshCw className="mr-2 h-4 w-4" />
                                Reprocess
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem className="text-red-600">
                                <Trash2 className="mr-2 h-4 w-4" />
                                Delete
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="processing" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Processing Queue</CardTitle>
                <CardDescription>
                  Monitor document processing and RAG indexing status
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {documents
                    .filter(
                      (doc) =>
                        doc.status === "processing" ||
                        doc.status === "indexing",
                    )
                    .map((doc) => (
                      <div
                        key={doc.id}
                        className="flex items-center justify-between p-4 border rounded-lg"
                      >
                        <div className="flex items-center space-x-3">
                          {getFileIcon(doc.type)}
                          <div>
                            <p className="font-medium">{doc.name}</p>
                            <p className="text-sm text-muted-foreground">
                              {doc.status === "processing"
                                ? "Processing document..."
                                : "Creating embeddings..."}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          {getStatusBadge(doc.status)}
                          <Button variant="ghost" size="sm">
                            <XCircle className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))}

                  {documents.filter(
                    (doc) =>
                      doc.status === "processing" || doc.status === "indexing",
                  ).length === 0 && (
                    <div className="text-center py-12">
                      <CheckCircle className="h-16 w-16 mx-auto text-muted-foreground" />
                      <p className="mt-4 text-muted-foreground">
                        No documents currently processing
                      </p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="links" className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>Agent Links</CardTitle>
                  <CardDescription>
                    Documents linked to AI agents for context
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {availableAgents.map((agent) => {
                      const linkedDocs = documents.filter((doc) =>
                        doc.linkedAgents.includes(agent),
                      );
                      return (
                        <div
                          key={agent}
                          className="flex items-center justify-between p-3 border rounded-lg"
                        >
                          <div className="flex items-center space-x-2">
                            <Bot className="h-4 w-4" />
                            <span className="font-medium">{agent}</span>
                          </div>
                          <Badge variant="outline">
                            {linkedDocs.length} docs
                          </Badge>
                        </div>
                      );
                    })}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Workflow Links</CardTitle>
                  <CardDescription>
                    Documents linked to workflows for processing
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {availableWorkflows.map((workflow) => {
                      const linkedDocs = documents.filter((doc) =>
                        doc.linkedWorkflows.includes(workflow),
                      );
                      return (
                        <div
                          key={workflow}
                          className="flex items-center justify-between p-3 border rounded-lg"
                        >
                          <div className="flex items-center space-x-2">
                            <GitBranch className="h-4 w-4" />
                            <span className="font-medium">{workflow}</span>
                          </div>
                          <Badge variant="outline">
                            {linkedDocs.length} docs
                          </Badge>
                        </div>
                      );
                    })}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>

        {/* Link Management Dialog */}
        <Dialog open={linkDialogOpen} onOpenChange={setLinkDialogOpen}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Manage Links</DialogTitle>
              <DialogDescription>
                Link this document to agents and workflows for enhanced context
              </DialogDescription>
            </DialogHeader>
            {selectedDocument && (
              <div className="space-y-6">
                <div className="flex items-center space-x-3 p-4 bg-muted/50 rounded-lg">
                  {getFileIcon(selectedDocument.type)}
                  <div>
                    <p className="font-medium">{selectedDocument.name}</p>
                    <p className="text-sm text-muted-foreground">
                      {selectedDocument.size} • {selectedDocument.chunks} chunks
                    </p>
                  </div>
                </div>

                <div className="grid gap-6 md:grid-cols-2">
                  <div className="space-y-3">
                    <Label className="text-base font-medium">
                      Link to Agents
                    </Label>
                    <ScrollArea className="h-48 border rounded-lg p-3">
                      <div className="space-y-2">
                        {availableAgents.map((agent) => (
                          <div
                            key={agent}
                            className="flex items-center space-x-2"
                          >
                            <Checkbox
                              id={`agent-${agent}`}
                              defaultChecked={selectedDocument.linkedAgents.includes(
                                agent,
                              )}
                            />
                            <Label
                              htmlFor={`agent-${agent}`}
                              className="text-sm"
                            >
                              {agent}
                            </Label>
                          </div>
                        ))}
                      </div>
                    </ScrollArea>
                  </div>

                  <div className="space-y-3">
                    <Label className="text-base font-medium">
                      Link to Workflows
                    </Label>
                    <ScrollArea className="h-48 border rounded-lg p-3">
                      <div className="space-y-2">
                        {availableWorkflows.map((workflow) => (
                          <div
                            key={workflow}
                            className="flex items-center space-x-2"
                          >
                            <Checkbox
                              id={`workflow-${workflow}`}
                              defaultChecked={selectedDocument.linkedWorkflows.includes(
                                workflow,
                              )}
                            />
                            <Label
                              htmlFor={`workflow-${workflow}`}
                              className="text-sm"
                            >
                              {workflow}
                            </Label>
                          </div>
                        ))}
                      </div>
                    </ScrollArea>
                  </div>
                </div>
              </div>
            )}
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setLinkDialogOpen(false)}
              >
                Cancel
              </Button>
              <Button onClick={() => setLinkDialogOpen(false)}>
                Save Links
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
}
