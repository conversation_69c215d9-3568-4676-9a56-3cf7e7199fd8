"use client";

import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Wrench,
  Plus,
  Play,
  Save,
  Code,
  Settings,
  TestTube,
  CheckCircle,
  XCircle,
  Clock,
  MoreHorizontal,
  Edit,
  Trash2,
  Copy,
  ExternalLink,
  Key,
  Globe,
  Database,
  Mail,
  MessageSquare,
  FileText,
  Calendar,
  DollarSign,
} from "lucide-react";

interface ToolBuilderProps {
  className?: string;
}

interface Parameter {
  id: string;
  name: string;
  type: string;
  required: boolean;
  description: string;
  defaultValue?: string;
}

interface Tool {
  id: string;
  name: string;
  description: string;
  category: string;
  status: "draft" | "testing" | "deployed" | "error";
  apiEndpoint: string;
  method: string;
  lastTested: string;
  usageCount: number;
}

export default function ToolBuilder({ className = "" }: ToolBuilderProps) {
  const [activeTab, setActiveTab] = useState("builder");
  const [selectedTool, setSelectedTool] = useState<Tool | null>(null);
  const [testDialogOpen, setTestDialogOpen] = useState(false);
  const [parameterDialogOpen, setParameterDialogOpen] = useState(false);
  const [testResults, setTestResults] = useState<any>(null);
  const [isTestingTool, setIsTestingTool] = useState(false);

  // Tool configuration state
  const [toolName, setToolName] = useState("");
  const [toolDescription, setToolDescription] = useState("");
  const [toolCategory, setToolCategory] = useState("api");
  const [apiEndpoint, setApiEndpoint] = useState("");
  const [httpMethod, setHttpMethod] = useState("GET");
  const [authType, setAuthType] = useState("none");
  const [apiKey, setApiKey] = useState("");
  const [requiresAuth, setRequiresAuth] = useState(false);

  const [inputParameters, setInputParameters] = useState<Parameter[]>([
    {
      id: "1",
      name: "message",
      type: "string",
      required: true,
      description: "The message to send",
    },
  ]);

  const [outputParameters, setOutputParameters] = useState<Parameter[]>([
    {
      id: "1",
      name: "success",
      type: "boolean",
      required: true,
      description: "Whether the operation was successful",
    },
  ]);

  const tools: Tool[] = [
    {
      id: "1",
      name: "Email Sender",
      description: "Send emails via SMTP integration",
      category: "Communication",
      status: "deployed",
      apiEndpoint: "https://api.example.com/send-email",
      method: "POST",
      lastTested: "2 hours ago",
      usageCount: 156,
    },
    {
      id: "2",
      name: "Slack Notifier",
      description: "Send notifications to Slack channels",
      category: "Communication",
      status: "deployed",
      apiEndpoint: "https://hooks.slack.com/services/...",
      method: "POST",
      lastTested: "1 day ago",
      usageCount: 89,
    },
    {
      id: "3",
      name: "Database Query",
      description: "Execute SQL queries on connected databases",
      category: "Data",
      status: "testing",
      apiEndpoint: "https://api.example.com/query",
      method: "POST",
      lastTested: "5 minutes ago",
      usageCount: 23,
    },
    {
      id: "4",
      name: "Weather API",
      description: "Get current weather information",
      category: "External API",
      status: "draft",
      apiEndpoint: "https://api.openweathermap.org/data/2.5/weather",
      method: "GET",
      lastTested: "Never",
      usageCount: 0,
    },
  ];

  const categories = [
    {
      value: "api",
      label: "External API",
      icon: <Globe className="h-4 w-4" />,
    },
    {
      value: "database",
      label: "Database",
      icon: <Database className="h-4 w-4" />,
    },
    {
      value: "communication",
      label: "Communication",
      icon: <Mail className="h-4 w-4" />,
    },
    {
      value: "messaging",
      label: "Messaging",
      icon: <MessageSquare className="h-4 w-4" />,
    },
    {
      value: "document",
      label: "Document",
      icon: <FileText className="h-4 w-4" />,
    },
    {
      value: "calendar",
      label: "Calendar",
      icon: <Calendar className="h-4 w-4" />,
    },
    {
      value: "payment",
      label: "Payment",
      icon: <DollarSign className="h-4 w-4" />,
    },
  ];

  const parameterTypes = [
    "string",
    "number",
    "boolean",
    "array",
    "object",
    "date",
    "email",
    "url",
  ];

  const httpMethods = ["GET", "POST", "PUT", "DELETE", "PATCH"];
  const authTypes = ["none", "api-key", "bearer", "basic", "oauth2"];

  const handleTestTool = async () => {
    setIsTestingTool(true);
    // Simulate API testing
    setTimeout(() => {
      setTestResults({
        success: true,
        statusCode: 200,
        responseTime: "245ms",
        response: {
          message: "Tool executed successfully",
          data: { result: "Sample response data" },
        },
      });
      setIsTestingTool(false);
    }, 2000);
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      draft: "secondary",
      testing: "outline",
      deployed: "default",
      error: "destructive",
    } as const;
    return (
      <Badge variant={variants[status as keyof typeof variants]}>
        {status}
      </Badge>
    );
  };

  const getCategoryIcon = (category: string) => {
    const categoryData = categories.find(
      (c) => c.label.toLowerCase() === category.toLowerCase(),
    );
    return categoryData?.icon || <Wrench className="h-4 w-4" />;
  };

  const addParameter = (type: "input" | "output") => {
    const newParam: Parameter = {
      id: Date.now().toString(),
      name: "",
      type: "string",
      required: false,
      description: "",
    };

    if (type === "input") {
      setInputParameters([...inputParameters, newParam]);
    } else {
      setOutputParameters([...outputParameters, newParam]);
    }
  };

  const removeParameter = (id: string, type: "input" | "output") => {
    if (type === "input") {
      setInputParameters(inputParameters.filter((p) => p.id !== id));
    } else {
      setOutputParameters(outputParameters.filter((p) => p.id !== id));
    }
  };

  return (
    <div className={`bg-background min-h-screen p-6 ${className}`}>
      <div className="max-w-7xl mx-auto">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Tool Builder</h1>
            <p className="text-muted-foreground">
              Create and manage API integrations and custom tools
            </p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <Code className="mr-2 h-4 w-4" />
              View Code
            </Button>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              New Tool
            </Button>
          </div>
        </div>

        <Tabs
          value={activeTab}
          onValueChange={setActiveTab}
          className="space-y-6"
        >
          <TabsList>
            <TabsTrigger value="builder">
              <Wrench className="mr-2 h-4 w-4" />
              Builder
            </TabsTrigger>
            <TabsTrigger value="tools">
              <Settings className="mr-2 h-4 w-4" />
              My Tools
            </TabsTrigger>
            <TabsTrigger value="testing">
              <TestTube className="mr-2 h-4 w-4" />
              Testing
            </TabsTrigger>
          </TabsList>

          <TabsContent value="builder" className="space-y-6">
            <div className="grid gap-6 lg:grid-cols-2">
              {/* Tool Configuration */}
              <Card>
                <CardHeader>
                  <CardTitle>Tool Configuration</CardTitle>
                  <CardDescription>
                    Configure your tool's basic settings and API details
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="tool-name">Tool Name</Label>
                    <Input
                      id="tool-name"
                      placeholder="My Custom Tool"
                      value={toolName}
                      onChange={(e) => setToolName(e.target.value)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="tool-description">Description</Label>
                    <Textarea
                      id="tool-description"
                      placeholder="Describe what this tool does..."
                      value={toolDescription}
                      onChange={(e) => setToolDescription(e.target.value)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="tool-category">Category</Label>
                    <Select
                      value={toolCategory}
                      onValueChange={setToolCategory}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {categories.map((category) => (
                          <SelectItem
                            key={category.value}
                            value={category.value}
                          >
                            <div className="flex items-center space-x-2">
                              {category.icon}
                              <span>{category.label}</span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <Separator />

                  <div className="space-y-2">
                    <Label htmlFor="api-endpoint">API Endpoint</Label>
                    <Input
                      id="api-endpoint"
                      placeholder="https://api.example.com/endpoint"
                      value={apiEndpoint}
                      onChange={(e) => setApiEndpoint(e.target.value)}
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="http-method">HTTP Method</Label>
                      <Select value={httpMethod} onValueChange={setHttpMethod}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {httpMethods.map((method) => (
                            <SelectItem key={method} value={method}>
                              {method}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="auth-type">Authentication</Label>
                      <Select value={authType} onValueChange={setAuthType}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {authTypes.map((type) => (
                            <SelectItem key={type} value={type}>
                              {type === "none" ? "None" : type.toUpperCase()}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  {authType !== "none" && (
                    <div className="space-y-2">
                      <Label htmlFor="api-key">API Key / Token</Label>
                      <Input
                        id="api-key"
                        type="password"
                        placeholder="Enter your API key or token"
                        value={apiKey}
                        onChange={(e) => setApiKey(e.target.value)}
                      />
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Parameters */}
              <Card>
                <CardHeader>
                  <CardTitle>Parameters</CardTitle>
                  <CardDescription>
                    Define input and output parameters for your tool
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Tabs defaultValue="input" className="space-y-4">
                    <TabsList className="grid w-full grid-cols-2">
                      <TabsTrigger value="input">Input Parameters</TabsTrigger>
                      <TabsTrigger value="output">
                        Output Parameters
                      </TabsTrigger>
                    </TabsList>

                    <TabsContent value="input" className="space-y-4">
                      <div className="flex justify-between items-center">
                        <h4 className="font-medium">Input Parameters</h4>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => addParameter("input")}
                        >
                          <Plus className="h-4 w-4" />
                        </Button>
                      </div>
                      <ScrollArea className="h-64">
                        <div className="space-y-3">
                          {inputParameters.map((param) => (
                            <div
                              key={param.id}
                              className="border rounded-lg p-3 space-y-2"
                            >
                              <div className="flex justify-between items-center">
                                <Input
                                  placeholder="Parameter name"
                                  value={param.name}
                                  className="flex-1 mr-2"
                                />
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() =>
                                    removeParameter(param.id, "input")
                                  }
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </div>
                              <div className="grid grid-cols-2 gap-2">
                                <Select defaultValue={param.type}>
                                  <SelectTrigger>
                                    <SelectValue />
                                  </SelectTrigger>
                                  <SelectContent>
                                    {parameterTypes.map((type) => (
                                      <SelectItem key={type} value={type}>
                                        {type}
                                      </SelectItem>
                                    ))}
                                  </SelectContent>
                                </Select>
                                <div className="flex items-center space-x-2">
                                  <Switch defaultChecked={param.required} />
                                  <Label className="text-sm">Required</Label>
                                </div>
                              </div>
                              <Input
                                placeholder="Description"
                                value={param.description}
                                className="text-sm"
                              />
                            </div>
                          ))}
                        </div>
                      </ScrollArea>
                    </TabsContent>

                    <TabsContent value="output" className="space-y-4">
                      <div className="flex justify-between items-center">
                        <h4 className="font-medium">Output Parameters</h4>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => addParameter("output")}
                        >
                          <Plus className="h-4 w-4" />
                        </Button>
                      </div>
                      <ScrollArea className="h-64">
                        <div className="space-y-3">
                          {outputParameters.map((param) => (
                            <div
                              key={param.id}
                              className="border rounded-lg p-3 space-y-2"
                            >
                              <div className="flex justify-between items-center">
                                <Input
                                  placeholder="Parameter name"
                                  value={param.name}
                                  className="flex-1 mr-2"
                                />
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() =>
                                    removeParameter(param.id, "output")
                                  }
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </div>
                              <div className="grid grid-cols-2 gap-2">
                                <Select defaultValue={param.type}>
                                  <SelectTrigger>
                                    <SelectValue />
                                  </SelectTrigger>
                                  <SelectContent>
                                    {parameterTypes.map((type) => (
                                      <SelectItem key={type} value={type}>
                                        {type}
                                      </SelectItem>
                                    ))}
                                  </SelectContent>
                                </Select>
                                <div className="flex items-center space-x-2">
                                  <Switch defaultChecked={param.required} />
                                  <Label className="text-sm">Required</Label>
                                </div>
                              </div>
                              <Input
                                placeholder="Description"
                                value={param.description}
                                className="text-sm"
                              />
                            </div>
                          ))}
                        </div>
                      </ScrollArea>
                    </TabsContent>
                  </Tabs>
                </CardContent>
              </Card>
            </div>

            <div className="flex justify-end space-x-2">
              <Button variant="outline">
                <Save className="mr-2 h-4 w-4" />
                Save Draft
              </Button>
              <Button onClick={() => setTestDialogOpen(true)}>
                <TestTube className="mr-2 h-4 w-4" />
                Test Tool
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="tools" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>My Tools ({tools.length})</CardTitle>
                <CardDescription>
                  Manage your created tools and their configurations
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Tool</TableHead>
                      <TableHead>Category</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Usage</TableHead>
                      <TableHead>Last Tested</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {tools.map((tool) => (
                      <TableRow key={tool.id}>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            {getCategoryIcon(tool.category)}
                            <div>
                              <p className="font-medium">{tool.name}</p>
                              <p className="text-sm text-muted-foreground">
                                {tool.description}
                              </p>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>{tool.category}</TableCell>
                        <TableCell>{getStatusBadge(tool.status)}</TableCell>
                        <TableCell>{tool.usageCount} calls</TableCell>
                        <TableCell className="text-sm text-muted-foreground">
                          {tool.lastTested}
                        </TableCell>
                        <TableCell>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem>
                                <Edit className="mr-2 h-4 w-4" />
                                Edit Tool
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <TestTube className="mr-2 h-4 w-4" />
                                Test Tool
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <Copy className="mr-2 h-4 w-4" />
                                Duplicate
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <ExternalLink className="mr-2 h-4 w-4" />
                                View API Docs
                              </DropdownMenuItem>
                              <DropdownMenuItem className="text-red-600">
                                <Trash2 className="mr-2 h-4 w-4" />
                                Delete Tool
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="testing" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Tool Testing</CardTitle>
                <CardDescription>
                  Test your tools before deployment
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-center py-12">
                  <TestTube className="h-16 w-16 mx-auto text-muted-foreground" />
                  <p className="mt-4 text-muted-foreground">
                    Select a tool from the Builder tab to test it here
                  </p>
                  <Button
                    className="mt-4"
                    onClick={() => setActiveTab("builder")}
                  >
                    Go to Builder
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Test Tool Dialog */}
        <Dialog open={testDialogOpen} onOpenChange={setTestDialogOpen}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Test Tool</DialogTitle>
              <DialogDescription>
                Test your tool configuration with sample data
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label>Test Parameters</Label>
                <div className="border rounded-lg p-4 space-y-3">
                  {inputParameters.map((param) => (
                    <div key={param.id} className="space-y-1">
                      <Label className="text-sm">
                        {param.name}{" "}
                        {param.required && (
                          <span className="text-red-500">*</span>
                        )}
                      </Label>
                      <Input placeholder={`Enter ${param.name}`} />
                      <p className="text-xs text-muted-foreground">
                        {param.description}
                      </p>
                    </div>
                  ))}
                </div>
              </div>

              {testResults && (
                <div className="space-y-2">
                  <Label>Test Results</Label>
                  <div className="border rounded-lg p-4 bg-muted/50">
                    <div className="flex items-center space-x-2 mb-2">
                      {testResults.success ? (
                        <CheckCircle className="h-4 w-4 text-green-500" />
                      ) : (
                        <XCircle className="h-4 w-4 text-red-500" />
                      )}
                      <span className="font-medium">
                        Status: {testResults.statusCode}
                      </span>
                      <Badge variant="outline">
                        {testResults.responseTime}
                      </Badge>
                    </div>
                    <pre className="text-sm bg-background p-2 rounded border overflow-auto">
                      {JSON.stringify(testResults.response, null, 2)}
                    </pre>
                  </div>
                </div>
              )}
            </div>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setTestDialogOpen(false)}
              >
                Close
              </Button>
              <Button onClick={handleTestTool} disabled={isTestingTool}>
                {isTestingTool ? (
                  <>
                    <Clock className="mr-2 h-4 w-4 animate-spin" />
                    Testing...
                  </>
                ) : (
                  <>
                    <Play className="mr-2 h-4 w-4" />
                    Run Test
                  </>
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
}
